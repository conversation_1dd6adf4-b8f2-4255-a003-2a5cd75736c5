{% extends "layout.html" %}

{% block title %}Home{% endblock %}

{% block content %}
<div class="hero-section text-center py-5">
    <div class="container">
        <h1 class="display-3 mb-4">NeuroSurge</h1>
        <p class="lead mb-4">Interactive 3D Brain Tumor Surgery Planning Platform</p>
        <div class="row justify-content-center">
            <div class="col-md-8">
                <p class="mb-4">NeuroSurge is an advanced platform for neurosurgeons and medical students to visualize, plan, and simulate brain tumor surgeries using interactive 3D models.</p>
                <a href="/simulator" class="btn btn-primary btn-lg me-2">Try 3D Simulator</a>
                <a href="#explore" class="btn btn-outline-light btn-lg">Explore Tumor Types</a>
            </div>
        </div>
    </div>
</div>

<div class="container mt-5" id="explore">
    <div class="row mb-5">
        <div class="col-md-6">
            <div class="brain-model-container">
                <div id="brain-model"></div>
            </div>
        </div>
        <div class="col-md-6">
            <h2 class="mb-4">Interactive 3D Brain Visualization</h2>
            <p>NeuroSurge provides detailed 3D models of the brain and various tumor types, allowing surgeons to:</p>
            <ul class="feature-list">
                <li><i class="fas fa-cube me-2"></i> Visualize tumors in three dimensions</li>
                <li><i class="fas fa-arrows-alt me-2"></i> Rotate, zoom, and manipulate 3D models</li>
                <li><i class="fas fa-route me-2"></i> Plan optimal surgical approaches</li>
                <li><i class="fas fa-brain me-2"></i> Identify critical brain structures</li>
                <li><i class="fas fa-user-md me-2"></i> Enhance surgical training and education</li>
            </ul>
            <p class="mt-3">Use our platform to improve surgical outcomes and reduce risks through better planning and visualization.</p>
        </div>
    </div>

    <h2 class="text-center mb-4">Explore Brain Tumor Types</h2>
    <div class="row mb-5">
        <div class="col-md-3 mb-4">
            <div class="card tumor-card h-100">
                <div class="card-body">
                    <h3 class="card-title">Glioblastoma</h3>
                    <p class="card-text">A fast-growing and aggressive brain tumor that forms from glial cells.</p>
                    <div class="tumor-preview" id="glioblastoma-preview"></div>
                    <a href="/tumor/glioblastoma" class="btn btn-outline-primary mt-3 w-100">Learn More</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card tumor-card h-100">
                <div class="card-body">
                    <h3 class="card-title">Meningioma</h3>
                    <p class="card-text">A tumor that forms in the meninges, the membranes that surround the brain and spinal cord.</p>
                    <div class="tumor-preview" id="meningioma-preview"></div>
                    <a href="/tumor/meningioma" class="btn btn-outline-primary mt-3 w-100">Learn More</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card tumor-card h-100">
                <div class="card-body">
                    <h3 class="card-title">Acoustic Neuroma</h3>
                    <p class="card-text">A benign tumor that develops on the nerve connecting the ear to the brain.</p>
                    <div class="tumor-preview" id="acoustic-preview"></div>
                    <a href="/tumor/acoustic_neuroma" class="btn btn-outline-primary mt-3 w-100">Learn More</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card tumor-card h-100">
                <div class="card-body">
                    <h3 class="card-title">Pituitary Adenoma</h3>
                    <p class="card-text">A tumor that forms in the pituitary gland at the base of the brain.</p>
                    <div class="tumor-preview" id="pituitary-preview"></div>
                    <a href="/tumor/pituitary_adenoma" class="btn btn-outline-primary mt-3 w-100">Learn More</a>
                </div>
            </div>
        </div>
    </div>

    <h2 class="text-center mb-4">Surgical Approaches</h2>
    <div class="row mb-5">
        <div class="col-md-3 mb-4">
            <div class="card approach-card h-100">
                <div class="card-body">
                    <h3 class="card-title">Craniotomy</h3>
                    <p class="card-text">A surgical procedure where part of the skull is removed to access the brain.</p>
                    <a href="/approach/craniotomy" class="btn btn-outline-primary mt-3 w-100">Learn More</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card approach-card h-100">
                <div class="card-body">
                    <h3 class="card-title">Transsphenoidal</h3>
                    <p class="card-text">An approach through the nose and sphenoid sinus to reach tumors at the base of the brain.</p>
                    <a href="/approach/transsphenoidal" class="btn btn-outline-primary mt-3 w-100">Learn More</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card approach-card h-100">
                <div class="card-body">
                    <h3 class="card-title">Endoscopic</h3>
                    <p class="card-text">Minimally invasive surgery using an endoscope (a thin tube with a light and camera).</p>
                    <a href="/approach/endoscopic" class="btn btn-outline-primary mt-3 w-100">Learn More</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card approach-card h-100">
                <div class="card-body">
                    <h3 class="card-title">Stereotactic Radiosurgery</h3>
                    <p class="card-text">A non-invasive procedure using precisely focused radiation to treat tumors.</p>
                    <a href="/approach/stereotactic" class="btn btn-outline-primary mt-3 w-100">Learn More</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Main brain model
    const brainContainer = document.getElementById('brain-model');
    let brainScene, brainCamera, brainRenderer, brainControls, brainModel;

    function initBrainModel() {
        // Create scene
        brainScene = new THREE.Scene();
        brainScene.background = new THREE.Color(0x111111);

        // Create camera
        brainCamera = new THREE.PerspectiveCamera(75, brainContainer.clientWidth / brainContainer.clientHeight, 0.1, 1000);
        brainCamera.position.z = 200;

        // Create renderer
        brainRenderer = new THREE.WebGLRenderer({ antialias: true });
        brainRenderer.setSize(brainContainer.clientWidth, brainContainer.clientHeight);
        brainContainer.appendChild(brainRenderer.domElement);

        // Add controls
        brainControls = new THREE.OrbitControls(brainCamera, brainRenderer.domElement);
        brainControls.enableDamping = true;
        brainControls.dampingFactor = 0.25;

        // Add lights
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        brainScene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(0, 1, 1);
        brainScene.add(directionalLight);

        // Create a simple brain model (sphere with bumps)
        const brainGeometry = new THREE.SphereGeometry(50, 32, 32);
        const brainMaterial = new THREE.MeshPhongMaterial({
            color: 0xe0a9a9,
            shininess: 10,
            transparent: true,
            opacity: 0.9
        });

        brainModel = new THREE.Mesh(brainGeometry, brainMaterial);
        brainScene.add(brainModel);

        // Add a tumor (red sphere)
        const tumorGeometry = new THREE.SphereGeometry(10, 32, 32);
        const tumorMaterial = new THREE.MeshPhongMaterial({ color: 0xff0000 });
        const tumor = new THREE.Mesh(tumorGeometry, tumorMaterial);
        tumor.position.set(30, 20, 30);
        brainScene.add(tumor);

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            brainControls.update();
            brainRenderer.render(brainScene, brainCamera);
        }
        animate();

        // Handle window resize
        window.addEventListener('resize', function() {
            brainCamera.aspect = brainContainer.clientWidth / brainContainer.clientHeight;
            brainCamera.updateProjectionMatrix();
            brainRenderer.setSize(brainContainer.clientWidth, brainContainer.clientHeight);
        });
    }

    // Initialize tumor preview models
    function initTumorPreviews() {
        const previewContainers = [
            { id: 'glioblastoma-preview', color: 0xff0000, position: { x: 0, y: 0, z: 0 } },
            { id: 'meningioma-preview', color: 0x00ff00, position: { x: 0, y: 0, z: 0 } },
            { id: 'acoustic-preview', color: 0x0000ff, position: { x: 0, y: 0, z: 0 } },
            { id: 'pituitary-preview', color: 0xffff00, position: { x: 0, y: 0, z: 0 } }
        ];

        previewContainers.forEach(container => {
            const element = document.getElementById(container.id);
            if (!element) return;

            // Create scene
            const scene = new THREE.Scene();
            scene.background = new THREE.Color(0xf8f9fa);

            // Create camera
            const camera = new THREE.PerspectiveCamera(75, element.clientWidth / element.clientHeight, 0.1, 1000);
            camera.position.z = 5;

            // Create renderer
            const renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(element.clientWidth, element.clientHeight);
            element.appendChild(renderer.domElement);

            // Add controls
            const controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.25;

            // Add lights
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(0, 1, 1);
            scene.add(directionalLight);

            // Create tumor model
            const geometry = new THREE.SphereGeometry(1, 32, 32);
            const material = new THREE.MeshPhongMaterial({ color: container.color });
            const tumor = new THREE.Mesh(geometry, material);
            tumor.position.set(container.position.x, container.position.y, container.position.z);
            scene.add(tumor);

            // Animation loop
            function animate() {
                requestAnimationFrame(animate);
                tumor.rotation.x += 0.01;
                tumor.rotation.y += 0.01;
                controls.update();
                renderer.render(scene, camera);
            }
            animate();
        });
    }

    // Initialize all 3D models
    initBrainModel();
    initTumorPreviews();
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #4ca1af 100%);
    color: white;
    padding: 80px 0;
    margin-top: -1.5rem;
}

.brain-model-container {
    width: 100%;
    height: 400px;
    background-color: #111;
    border-radius: 10px;
    overflow: hidden;
}

#brain-model {
    width: 100%;
    height: 100%;
}

.feature-list {
    list-style-type: none;
    padding-left: 0;
}

.feature-list li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.feature-list li:last-child {
    border-bottom: none;
}

.tumor-card, .approach-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.tumor-card:hover, .approach-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.tumor-preview {
    width: 100%;
    height: 150px;
    background-color: #f8f9fa;
    border-radius: 5px;
    margin-top: 15px;
}
</style>
{% endblock %}
