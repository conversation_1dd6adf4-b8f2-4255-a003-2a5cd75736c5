{% extends "layout.html" %}

{% block title %}{{ tumor.name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-5">
        <div class="col-md-6">
            <h1 class="mb-4">{{ tumor.name }}</h1>
            <p class="lead">{{ tumor.description }}</p>
            
            <div class="mt-4">
                <h3>Risk Factors</h3>
                <ul class="list-group mb-4">
                    {% for factor in tumor.risk_factors %}
                    <li class="list-group-item"><i class="fas fa-exclamation-triangle text-warning me-2"></i> {{ factor }}</li>
                    {% endfor %}
                </ul>
                
                <h3>Common Symptoms</h3>
                <ul class="list-group mb-4">
                    {% for symptom in tumor.symptoms %}
                    <li class="list-group-item"><i class="fas fa-notes-medical text-danger me-2"></i> {{ symptom }}</li>
                    {% endfor %}
                </ul>
                
                <h3>Treatment Approaches</h3>
                <ul class="list-group mb-4">
                    {% for approach in tumor.treatment_approaches %}
                    <li class="list-group-item"><i class="fas fa-hospital text-primary me-2"></i> {{ approach }}</li>
                    {% endfor %}
                </ul>
                
                <h3>Surgical Challenges</h3>
                <ul class="list-group mb-4">
                    {% for challenge in tumor.surgical_challenges %}
                    <li class="list-group-item"><i class="fas fa-exclamation-circle text-danger me-2"></i> {{ challenge }}</li>
                    {% endfor %}
                </ul>
            </div>
            
            <div class="mt-4">
                <a href="/simulator?tumor={{ tumor_type }}" class="btn btn-primary me-2">
                    <i class="fas fa-vr-cardboard me-2"></i>Simulate in 3D
                </a>
                <a href="/" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Home
                </a>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="tumor-model-container">
                <div id="tumor-model"></div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header bg-dark text-white">
                    <h3 class="mb-0">Surgical Planning</h3>
                </div>
                <div class="card-body">
                    <p>Use the 3D model to explore the tumor's location and relationship to critical brain structures. You can:</p>
                    <ul>
                        <li>Rotate the model by dragging</li>
                        <li>Zoom in/out using the mouse wheel</li>
                        <li>Pan by holding Shift while dragging</li>
                    </ul>
                    <p>Toggle visibility of different brain structures using the controls below:</p>
                    
                    <div class="form-check form-switch mb-2">
                        <input class="form-check-input" type="checkbox" id="showBrain" checked>
                        <label class="form-check-label" for="showBrain">Brain Tissue</label>
                    </div>
                    
                    <div class="form-check form-switch mb-2">
                        <input class="form-check-input" type="checkbox" id="showTumor" checked>
                        <label class="form-check-label" for="showTumor">Tumor</label>
                    </div>
                    
                    <div class="form-check form-switch mb-2">
                        <input class="form-check-input" type="checkbox" id="showVessels">
                        <label class="form-check-label" for="showVessels">Blood Vessels</label>
                    </div>
                    
                    <div class="form-check form-switch mb-2">
                        <input class="form-check-input" type="checkbox" id="showNerves">
                        <label class="form-check-label" for="showNerves">Nerves</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const modelContainer = document.getElementById('tumor-model');
    let scene, camera, renderer, controls;
    let brainModel, tumorModel, vesselsModel, nervesModel;
    
    // Initialize the 3D scene
    function init() {
        // Create scene
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x111111);
        
        // Create camera
        camera = new THREE.PerspectiveCamera(75, modelContainer.clientWidth / modelContainer.clientHeight, 0.1, 1000);
        camera.position.z = 200;
        
        // Create renderer
        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(modelContainer.clientWidth, modelContainer.clientHeight);
        modelContainer.appendChild(renderer.domElement);
        
        // Add controls
        controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.25;
        
        // Add lights
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(0, 1, 1);
        scene.add(directionalLight);
        
        // Create brain model
        const brainGeometry = new THREE.SphereGeometry(50, 32, 32);
        const brainMaterial = new THREE.MeshPhongMaterial({ 
            color: 0xe0a9a9,
            transparent: true,
            opacity: 0.7
        });
        brainModel = new THREE.Mesh(brainGeometry, brainMaterial);
        scene.add(brainModel);
        
        // Create tumor model based on tumor type
        const tumorGeometry = new THREE.SphereGeometry(15, 32, 32);
        let tumorColor;
        
        switch('{{ tumor_type }}') {
            case 'glioblastoma':
                tumorColor = 0xff0000; // Red
                break;
            case 'meningioma':
                tumorColor = 0x00ff00; // Green
                break;
            case 'acoustic_neuroma':
                tumorColor = 0x0000ff; // Blue
                break;
            case 'pituitary_adenoma':
                tumorColor = 0xffff00; // Yellow
                break;
            default:
                tumorColor = 0xff00ff; // Magenta
        }
        
        const tumorMaterial = new THREE.MeshPhongMaterial({ color: tumorColor });
        tumorModel = new THREE.Mesh(tumorGeometry, tumorMaterial);
        
        // Position tumor based on type
        if ('{{ tumor_type }}' === 'glioblastoma') {
            tumorModel.position.set(30, 20, 30);
        } else if ('{{ tumor_type }}' === 'meningioma') {
            tumorModel.position.set(-20, 40, 10);
        } else if ('{{ tumor_type }}' === 'acoustic_neuroma') {
            tumorModel.position.set(-40, -10, 20);
        } else if ('{{ tumor_type }}' === 'pituitary_adenoma') {
            tumorModel.position.set(0, -40, 0);
        }
        
        scene.add(tumorModel);
        
        // Create blood vessels (simple red tubes)
        const vesselGeometry = new THREE.CylinderGeometry(2, 2, 100, 8);
        const vesselMaterial = new THREE.MeshPhongMaterial({ color: 0xcc0000 });
        vesselsModel = new THREE.Group();
        
        const vessel1 = new THREE.Mesh(vesselGeometry, vesselMaterial);
        vessel1.position.set(20, 0, 0);
        vessel1.rotation.z = Math.PI / 2;
        vesselsModel.add(vessel1);
        
        const vessel2 = new THREE.Mesh(vesselGeometry, vesselMaterial);
        vessel2.position.set(-20, 0, 0);
        vessel2.rotation.z = Math.PI / 2;
        vesselsModel.add(vessel2);
        
        const vessel3 = new THREE.Mesh(vesselGeometry, vesselMaterial);
        vessel3.position.set(0, 20, 0);
        vesselsModel.add(vessel3);
        
        vesselsModel.visible = false;
        scene.add(vesselsModel);
        
        // Create nerves (simple yellow tubes)
        const nerveGeometry = new THREE.CylinderGeometry(1, 1, 120, 8);
        const nerveMaterial = new THREE.MeshPhongMaterial({ color: 0xffff00 });
        nervesModel = new THREE.Group();
        
        const nerve1 = new THREE.Mesh(nerveGeometry, nerveMaterial);
        nerve1.position.set(10, -10, 0);
        nerve1.rotation.z = Math.PI / 4;
        nervesModel.add(nerve1);
        
        const nerve2 = new THREE.Mesh(nerveGeometry, nerveMaterial);
        nerve2.position.set(-10, -10, 0);
        nerve2.rotation.z = -Math.PI / 4;
        nervesModel.add(nerve2);
        
        nervesModel.visible = false;
        scene.add(nervesModel);
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }
        animate();
        
        // Handle window resize
        window.addEventListener('resize', function() {
            camera.aspect = modelContainer.clientWidth / modelContainer.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(modelContainer.clientWidth, modelContainer.clientHeight);
        });
        
        // Toggle visibility of different structures
        document.getElementById('showBrain').addEventListener('change', function(e) {
            brainModel.visible = e.target.checked;
        });
        
        document.getElementById('showTumor').addEventListener('change', function(e) {
            tumorModel.visible = e.target.checked;
        });
        
        document.getElementById('showVessels').addEventListener('change', function(e) {
            vesselsModel.visible = e.target.checked;
        });
        
        document.getElementById('showNerves').addEventListener('change', function(e) {
            nervesModel.visible = e.target.checked;
        });
    }
    
    init();
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.tumor-model-container {
    width: 100%;
    height: 400px;
    background-color: #111;
    border-radius: 10px;
    overflow: hidden;
}

#tumor-model {
    width: 100%;
    height: 100%;
}

.list-group-item i {
    width: 20px;
    text-align: center;
}
</style>
{% endblock %}
