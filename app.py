from flask import Flask, render_template, request, jsonify

app = Flask(__name__)
app.config['SECRET_KEY'] = 'neurosurge-secret-key'

# Brain tumor data
tumor_types = {
    'glioblastoma': {
        'name': 'Glioblastoma',
        'description': 'A fast-growing and aggressive brain tumor that forms from glial cells.',
        'risk_factors': ['Age (typically over 50)', 'Radiation exposure', 'Genetic disorders'],
        'symptoms': ['Headaches', 'Nausea', 'Seizures', 'Memory problems', 'Personality changes'],
        'treatment_approaches': ['Surgery', 'Radiation therapy', 'Chemotherapy', 'Targeted therapy'],
        'surgical_challenges': ['Highly invasive', 'Often located near critical brain structures', 'Tends to recur'],
        'model_file': 'glioblastoma.glb'
    },
    'meningioma': {
        'name': 'Meningioma',
        'description': 'A tumor that forms in the meninges, the membranes that surround the brain and spinal cord.',
        'risk_factors': ['Radiation exposure', 'Female hormones', 'Genetic disorders'],
        'symptoms': ['Headaches', 'Vision problems', 'Hearing loss', 'Seizures'],
        'treatment_approaches': ['Surgery', 'Radiation therapy', 'Observation (for small, slow-growing tumors)'],
        'surgical_challenges': ['May be attached to critical blood vessels', 'Can be located at the skull base'],
        'model_file': 'meningioma.glb'
    },
    'acoustic_neuroma': {
        'name': 'Acoustic Neuroma',
        'description': 'A benign tumor that develops on the nerve connecting the ear to the brain.',
        'risk_factors': ['Neurofibromatosis type 2', 'Radiation exposure'],
        'symptoms': ['Hearing loss', 'Tinnitus', 'Balance problems', 'Facial numbness'],
        'treatment_approaches': ['Surgery', 'Radiation therapy', 'Observation'],
        'surgical_challenges': ['Proximity to facial nerve', 'Risk of hearing loss', 'Deep location'],
        'model_file': 'acoustic_neuroma.glb'
    },
    'pituitary_adenoma': {
        'name': 'Pituitary Adenoma',
        'description': 'A tumor that forms in the pituitary gland at the base of the brain.',
        'risk_factors': ['Multiple endocrine neoplasia type 1', 'Carney complex', 'Genetic factors'],
        'symptoms': ['Hormone imbalances', 'Vision problems', 'Headaches'],
        'treatment_approaches': ['Surgery', 'Medication', 'Radiation therapy'],
        'surgical_challenges': ['Deep location', 'Proximity to optic nerves and carotid arteries'],
        'model_file': 'pituitary_adenoma.glb'
    }
}

# Surgical approaches
surgical_approaches = {
    'craniotomy': {
        'name': 'Craniotomy',
        'description': 'A surgical procedure where part of the skull is removed to access the brain.',
        'advantages': ['Provides direct access to the tumor', 'Allows for maximum tumor removal'],
        'risks': ['Infection', 'Bleeding', 'Brain swelling', 'Neurological damage'],
        'best_for': ['Large tumors', 'Tumors in accessible areas']
    },
    'transsphenoidal': {
        'name': 'Transsphenoidal Surgery',
        'description': 'An approach through the nose and sphenoid sinus to reach tumors at the base of the brain.',
        'advantages': ['No visible scars', 'Shorter recovery time', 'Less pain'],
        'risks': ['CSF leak', 'Hormone imbalances', 'Nasal complications'],
        'best_for': ['Pituitary tumors', 'Some skull base tumors']
    },
    'endoscopic': {
        'name': 'Endoscopic Surgery',
        'description': 'Minimally invasive surgery using an endoscope (a thin tube with a light and camera).',
        'advantages': ['Smaller incisions', 'Less tissue damage', 'Faster recovery'],
        'risks': ['Limited field of view', 'Technical challenges'],
        'best_for': ['Ventricular tumors', 'Some skull base tumors']
    },
    'stereotactic': {
        'name': 'Stereotactic Radiosurgery',
        'description': 'A non-invasive procedure using precisely focused radiation to treat tumors.',
        'advantages': ['Non-invasive', 'No incision', 'Outpatient procedure'],
        'risks': ['Radiation effects', 'Not immediate tumor removal', 'Not suitable for all tumors'],
        'best_for': ['Small tumors', 'Deep tumors', 'Patients who cannot undergo surgery']
    }
}

# Routes
@app.route('/')
def home():
    return render_template('index.html')

@app.route('/tumor/<tumor_type>')
def tumor_detail(tumor_type):
    if tumor_type in tumor_types:
        return render_template('tumor_detail.html', tumor=tumor_types[tumor_type], tumor_type=tumor_type)
    return render_template('404.html'), 404

@app.route('/approach/<approach_type>')
def approach_detail(approach_type):
    if approach_type in surgical_approaches:
        return render_template('approach_detail.html', approach=surgical_approaches[approach_type], approach_type=approach_type)
    return render_template('404.html'), 404

@app.route('/simulator')
def simulator():
    return render_template('simulator.html', tumor_types=tumor_types, surgical_approaches=surgical_approaches)

@app.route('/api/tumor_types')
def api_tumor_types():
    return jsonify(tumor_types)

@app.route('/api/surgical_approaches')
def api_surgical_approaches():
    return jsonify(surgical_approaches)

if __name__ == '__main__':
    app.run(debug=True)
