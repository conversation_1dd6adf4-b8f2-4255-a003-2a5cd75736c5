/* NeuroSurge Custom Styles */

/* Global Styles */
:root {
    --primary-color: #4a90e2;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--dark-color);
    background-color: #f8f9fa;
    overflow-x: hidden;
}

/* Navbar Styles */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #ffffff;
}

/* Card Styles */
.card {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    border: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-top-left-radius: 10px !important;
    border-top-right-radius: 10px !important;
    font-weight: 600;
}

/* Button Styles */
.btn {
    border-radius: 5px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #3a7bc8;
    border-color: #3a7bc8;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-light {
    color: #fff;
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: #fff;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 5px;
    padding: 0.75rem 1rem;
    border: 1px solid #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(74, 144, 226, 0.25);
}

/* List Group Styles */
.list-group-item {
    border-left: none;
    border-right: none;
    padding: 1rem 1.25rem;
}

.list-group-item i {
    width: 20px;
    text-align: center;
    margin-right: 10px;
}

/* 3D Model Containers */
.brain-model-container,
.tumor-model-container,
.approach-model-container {
    width: 100%;
    height: 400px;
    background-color: #111;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Feature List */
.feature-list {
    list-style-type: none;
    padding-left: 0;
}

.feature-list li {
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-list li i {
    width: 24px;
    text-align: center;
    margin-right: 10px;
    color: var(--primary-color);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #4a90e2 100%);
    color: white;
    padding: 80px 0;
    margin-top: -1.5rem;
}

/* Tumor and Approach Cards */
.tumor-card, .approach-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.tumor-card:hover, .approach-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.tumor-preview {
    width: 100%;
    height: 150px;
    background-color: #f8f9fa;
    border-radius: 5px;
    margin-top: 15px;
}

/* Simulator Styles */
.simulator-container {
    height: calc(100vh - 56px);
    margin-top: -1.5rem;
    overflow: hidden;
}

#simulator-canvas {
    width: 100%;
    height: 100%;
    background-color: #111;
}

.simulator-controls {
    height: 100%;
    padding: 20px;
    background-color: #f8f9fa;
    overflow-y: auto;
}

/* Form Switch */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .display-3 {
        font-size: 2.5rem;
    }

    .brain-model-container,
    .tumor-model-container,
    .approach-model-container {
        height: 300px;
    }

    .simulator-container {
        height: auto;
    }

    #simulator-canvas {
        height: 50vh;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #3a7bc8;
}
