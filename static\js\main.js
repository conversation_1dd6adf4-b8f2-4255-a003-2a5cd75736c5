// NeuroSurge Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Enable tooltips everywhere
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Enable popovers everywhere
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');

    if (forms.length > 0) {
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                form.classList.add('was-validated');
            }, false);
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            if (this.getAttribute('href') !== '#') {
                e.preventDefault();

                const targetElement = document.querySelector(this.getAttribute('href'));
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            }
        });
    });

    // Add active class to current nav item
    const currentLocation = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        // Check if the link href matches the current path
        if (link.getAttribute('href') === currentLocation) {
            link.classList.add('active');
        }
        // Check if the current path starts with the link href (for dropdown items)
        else if (link.getAttribute('href') !== '/' &&
                 link.getAttribute('href') !== '#' &&
                 currentLocation.startsWith(link.getAttribute('href'))) {
            link.classList.add('active');
        }
    });

    // Handle dropdown active states
    const currentPath = window.location.pathname;

    // Check if current path is a tumor detail page
    if (currentPath.startsWith('/tumor/')) {
        document.getElementById('tumorDropdown')?.classList.add('active');
    }

    // Check if current path is an approach detail page
    if (currentPath.startsWith('/approach/')) {
        document.getElementById('approachDropdown')?.classList.add('active');
    }
});
