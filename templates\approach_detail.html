{% extends "layout.html" %}

{% block title %}{{ approach.name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-5">
        <div class="col-md-6">
            <h1 class="mb-4">{{ approach.name }}</h1>
            <p class="lead">{{ approach.description }}</p>
            
            <div class="mt-4">
                <h3>Advantages</h3>
                <ul class="list-group mb-4">
                    {% for advantage in approach.advantages %}
                    <li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i> {{ advantage }}</li>
                    {% endfor %}
                </ul>
                
                <h3>Risks and Limitations</h3>
                <ul class="list-group mb-4">
                    {% for risk in approach.risks %}
                    <li class="list-group-item"><i class="fas fa-exclamation-triangle text-warning me-2"></i> {{ risk }}</li>
                    {% endfor %}
                </ul>
                
                <h3>Best For</h3>
                <ul class="list-group mb-4">
                    {% for best in approach.best_for %}
                    <li class="list-group-item"><i class="fas fa-thumbs-up text-primary me-2"></i> {{ best }}</li>
                    {% endfor %}
                </ul>
            </div>
            
            <div class="mt-4">
                <a href="/simulator?approach={{ approach_type }}" class="btn btn-primary me-2">
                    <i class="fas fa-vr-cardboard me-2"></i>Simulate in 3D
                </a>
                <a href="/" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Home
                </a>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="approach-model-container">
                <div id="approach-model"></div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header bg-dark text-white">
                    <h3 class="mb-0">Surgical Approach Visualization</h3>
                </div>
                <div class="card-body">
                    <p>Use the 3D model to explore this surgical approach. The visualization shows:</p>
                    <ul>
                        <li>Entry point and trajectory</li>
                        <li>Structures encountered during the approach</li>
                        <li>Relationship to critical brain anatomy</li>
                    </ul>
                    <p>Interact with the model using:</p>
                    <ul>
                        <li>Rotate: Click and drag</li>
                        <li>Zoom: Mouse wheel</li>
                        <li>Pan: Shift + drag</li>
                    </ul>
                    
                    <div class="mt-4">
                        <h4>Approach Animation</h4>
                        <div class="d-flex justify-content-between">
                            <button id="playAnimation" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>Play Animation
                            </button>
                            <button id="resetAnimation" class="btn btn-secondary">
                                <i class="fas fa-undo me-2"></i>Reset
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const modelContainer = document.getElementById('approach-model');
    let scene, camera, renderer, controls;
    let brainModel, approachModel, instrumentModel;
    let animationInProgress = false;
    
    // Initialize the 3D scene
    function init() {
        // Create scene
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x111111);
        
        // Create camera
        camera = new THREE.PerspectiveCamera(75, modelContainer.clientWidth / modelContainer.clientHeight, 0.1, 1000);
        camera.position.z = 200;
        
        // Create renderer
        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(modelContainer.clientWidth, modelContainer.clientHeight);
        modelContainer.appendChild(renderer.domElement);
        
        // Add controls
        controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.25;
        
        // Add lights
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(0, 1, 1);
        scene.add(directionalLight);
        
        // Create brain model
        const brainGeometry = new THREE.SphereGeometry(50, 32, 32);
        const brainMaterial = new THREE.MeshPhongMaterial({ 
            color: 0xe0a9a9,
            transparent: true,
            opacity: 0.7
        });
        brainModel = new THREE.Mesh(brainGeometry, brainMaterial);
        scene.add(brainModel);
        
        // Create approach visualization based on approach type
        approachModel = new THREE.Group();
        
        // Add a tumor (red sphere)
        const tumorGeometry = new THREE.SphereGeometry(10, 32, 32);
        const tumorMaterial = new THREE.MeshPhongMaterial({ color: 0xff0000 });
        const tumor = new THREE.Mesh(tumorGeometry, tumorMaterial);
        
        // Position tumor and approach based on type
        let approachColor, approachStart, approachEnd;
        
        switch('{{ approach_type }}') {
            case 'craniotomy':
                tumor.position.set(20, 20, 20);
                approachColor = 0x00ffff;
                approachStart = new THREE.Vector3(20, 70, 20);
                approachEnd = new THREE.Vector3(20, 20, 20);
                break;
            case 'transsphenoidal':
                tumor.position.set(0, -20, 0);
                approachColor = 0xff00ff;
                approachStart = new THREE.Vector3(0, -70, 20);
                approachEnd = new THREE.Vector3(0, -20, 0);
                break;
            case 'endoscopic':
                tumor.position.set(-20, 20, 20);
                approachColor = 0xffff00;
                approachStart = new THREE.Vector3(-20, 70, 20);
                approachEnd = new THREE.Vector3(-20, 20, 20);
                break;
            case 'stereotactic':
                tumor.position.set(0, 0, 30);
                approachColor = 0x00ff00;
                // Multiple beams for stereotactic radiosurgery
                approachStart = null;
                approachEnd = null;
                
                // Create multiple radiation beams
                for (let i = 0; i < 8; i++) {
                    const angle = (i / 8) * Math.PI * 2;
                    const startX = Math.cos(angle) * 80;
                    const startY = Math.sin(angle) * 80;
                    
                    const beamGeometry = new THREE.CylinderGeometry(1, 1, 100, 8);
                    const beamMaterial = new THREE.MeshPhongMaterial({ 
                        color: 0x00ff00,
                        transparent: true,
                        opacity: 0.5
                    });
                    const beam = new THREE.Mesh(beamGeometry, beamMaterial);
                    beam.position.set(startX / 2, startY / 2, 0);
                    beam.lookAt(0, 0, 0);
                    beam.rotateX(Math.PI / 2);
                    approachModel.add(beam);
                }
                break;
            default:
                tumor.position.set(0, 0, 0);
                approachColor = 0xffffff;
                approachStart = new THREE.Vector3(0, 70, 0);
                approachEnd = new THREE.Vector3(0, 0, 0);
        }
        
        scene.add(tumor);
        
        // Create approach path (except for stereotactic which has multiple beams)
        if (approachStart && approachEnd) {
            const approachGeometry = new THREE.CylinderGeometry(3, 3, 
                approachStart.distanceTo(approachEnd), 8);
            const approachMaterial = new THREE.MeshPhongMaterial({ 
                color: approachColor,
                transparent: true,
                opacity: 0.5
            });
            const approachPath = new THREE.Mesh(approachGeometry, approachMaterial);
            
            // Position and rotate the cylinder to connect start and end points
            approachPath.position.set(
                (approachStart.x + approachEnd.x) / 2,
                (approachStart.y + approachEnd.y) / 2,
                (approachStart.z + approachEnd.z) / 2
            );
            
            // Calculate direction vector
            const direction = new THREE.Vector3().subVectors(approachEnd, approachStart);
            const normal = new THREE.Vector3(0, 1, 0);
            
            // Align cylinder with direction
            approachPath.quaternion.setFromUnitVectors(normal, direction.normalize());
            
            approachModel.add(approachPath);
        }
        
        scene.add(approachModel);
        
        // Create surgical instrument
        if ('{{ approach_type }}' !== 'stereotactic') {
            // Create a simple surgical instrument
            const instrumentGroup = new THREE.Group();
            
            // Instrument handle
            const handleGeometry = new THREE.CylinderGeometry(2, 2, 20, 16);
            const handleMaterial = new THREE.MeshPhongMaterial({ color: 0x888888 });
            const handle = new THREE.Mesh(handleGeometry, handleMaterial);
            handle.position.y = 10;
            instrumentGroup.add(handle);
            
            // Instrument tip
            const tipGeometry = new THREE.ConeGeometry(2, 10, 16);
            const tipMaterial = new THREE.MeshPhongMaterial({ color: 0xcccccc });
            const tip = new THREE.Mesh(tipGeometry, tipMaterial);
            tip.position.y = -5;
            instrumentGroup.add(tip);
            
            // Position the instrument at the start of the approach
            if (approachStart) {
                instrumentGroup.position.copy(approachStart);
                
                // Rotate to point toward the tumor
                instrumentGroup.lookAt(approachEnd);
                instrumentGroup.rotateX(Math.PI);
            }
            
            instrumentModel = instrumentGroup;
            scene.add(instrumentModel);
        }
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }
        animate();
        
        // Handle window resize
        window.addEventListener('resize', function() {
            camera.aspect = modelContainer.clientWidth / modelContainer.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(modelContainer.clientWidth, modelContainer.clientHeight);
        });
        
        // Animation controls
        document.getElementById('playAnimation').addEventListener('click', function() {
            if (animationInProgress || '{{ approach_type }}' === 'stereotactic') return;
            
            animationInProgress = true;
            const startPosition = approachStart.clone();
            const endPosition = approachEnd.clone();
            let progress = 0;
            
            function animateInstrument() {
                if (progress >= 1) {
                    animationInProgress = false;
                    return;
                }
                
                progress += 0.01;
                
                // Interpolate position
                instrumentModel.position.lerpVectors(startPosition, endPosition, progress);
                
                requestAnimationFrame(animateInstrument);
            }
            
            animateInstrument();
        });
        
        document.getElementById('resetAnimation').addEventListener('click', function() {
            if ('{{ approach_type }}' === 'stereotactic') return;
            
            animationInProgress = false;
            if (instrumentModel && approachStart) {
                instrumentModel.position.copy(approachStart);
            }
        });
    }
    
    init();
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.approach-model-container {
    width: 100%;
    height: 400px;
    background-color: #111;
    border-radius: 10px;
    overflow: hidden;
}

#approach-model {
    width: 100%;
    height: 100%;
}

.list-group-item i {
    width: 20px;
    text-align: center;
}
</style>
{% endblock %}
