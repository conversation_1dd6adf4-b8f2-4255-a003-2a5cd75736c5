# NeuroSurge - Interactive 3D Brain Tumor Surgery Planning Platform

NeuroSurge is an advanced web application that helps neurosurgeons and medical students visualize, plan, and simulate brain tumor surgeries using interactive 3D models.

## Features

- **Interactive 3D Visualization**: Explore detailed 3D models of the brain and various tumor types
- **Surgical Approach Simulation**: Visualize different surgical approaches and their relationship to critical brain structures
- **Educational Resources**: Learn about different brain tumor types and surgical techniques
- **Customizable Visualization**: Toggle visibility of different anatomical structures for better planning

## Technology Stack

- **Backend**: Flask (Python)
- **3D Visualization**: Three.js
- **Frontend**: HTML, CSS, JavaScript, Bootstrap 5

## Getting Started

### Prerequisites

- Python 3.8 or higher
- pip (Python package manager)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/neurosurge.git
   cd neurosurge
   ```

2. Create a virtual environment (optional but recommended):
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

4. Run the application:
   ```
   python app.py
   ```

5. Open your web browser and navigate to:
   ```
   http://127.0.0.1:5000/
   ```

## Project Structure

```
neurosurge/
├── app.py                  # Main Flask application
├── requirements.txt        # Python dependencies
├── static/                 # Static files
│   ├── css/
│   │   └── style.css       # Custom CSS
│   └── js/
│       └── main.js         # Custom JavaScript
└── templates/              # HTML templates
    ├── layout.html         # Base template
    ├── index.html          # Home page
    ├── tumor_detail.html   # Tumor information page
    ├── approach_detail.html # Surgical approach page
    ├── simulator.html      # 3D simulator page
    └── 404.html            # Error page
```

## Brain Tumor Types

The application includes information and 3D models for the following brain tumor types:

1. **Glioblastoma**: A fast-growing and aggressive brain tumor that forms from glial cells
2. **Meningioma**: A tumor that forms in the meninges, the membranes that surround the brain and spinal cord
3. **Acoustic Neuroma**: A benign tumor that develops on the nerve connecting the ear to the brain
4. **Pituitary Adenoma**: A tumor that forms in the pituitary gland at the base of the brain

## Surgical Approaches

The application demonstrates the following surgical approaches:

1. **Craniotomy**: A surgical procedure where part of the skull is removed to access the brain
2. **Transsphenoidal Surgery**: An approach through the nose and sphenoid sinus to reach tumors at the base of the brain
3. **Endoscopic Surgery**: Minimally invasive surgery using an endoscope (a thin tube with a light and camera)
4. **Stereotactic Radiosurgery**: A non-invasive procedure using precisely focused radiation to treat tumors

## Disclaimer

This application is for educational and demonstration purposes only. It should not be used as a substitute for professional medical advice, diagnosis, or treatment. The 3D models are simplified representations and not anatomically perfect.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
