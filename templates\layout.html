<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeuroSurge - {% block title %}{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-brain me-2"></i>NeuroSurge
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home me-1"></i> Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="tumorDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-disease me-1"></i> Tumor Types
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/tumor/glioblastoma">Glioblastoma</a></li>
                            <li><a class="dropdown-item" href="/tumor/meningioma">Meningioma</a></li>
                            <li><a class="dropdown-item" href="/tumor/acoustic_neuroma">Acoustic Neuroma</a></li>
                            <li><a class="dropdown-item" href="/tumor/pituitary_adenoma">Pituitary Adenoma</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="approachDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-procedures me-1"></i> Surgical Approaches
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/approach/craniotomy">Craniotomy</a></li>
                            <li><a class="dropdown-item" href="/approach/transsphenoidal">Transsphenoidal</a></li>
                            <li><a class="dropdown-item" href="/approach/endoscopic">Endoscopic</a></li>
                            <li><a class="dropdown-item" href="/approach/stereotactic">Stereotactic Radiosurgery</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/simulator"><i class="fas fa-vr-cardboard me-1"></i> 3D Simulator</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <main class="container-fluid py-4">
        {% block content %}{% endblock %}
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/loaders/GLTFLoader.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
