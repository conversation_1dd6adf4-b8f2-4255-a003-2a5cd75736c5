{% extends "layout.html" %}

{% block title %}3D Simulator{% endblock %}

{% block content %}
<div class="simulator-container">
    <div class="row g-0">
        <div class="col-md-9">
            <div id="simulator-canvas"></div>
        </div>
        <div class="col-md-3">
            <div class="simulator-controls">
                <h2>NeuroSurge Simulator</h2>
                <p>Interactive 3D brain tumor surgery planning tool</p>
                
                <div class="card mb-3">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">Tumor Selection</h5>
                    </div>
                    <div class="card-body">
                        <select id="tumor-select" class="form-select mb-3">
                            <option value="">Select Tumor Type</option>
                            {% for tumor_type, tumor in tumor_types.items() %}
                            <option value="{{ tumor_type }}">{{ tumor.name }}</option>
                            {% endfor %}
                        </select>
                        
                        <div id="tumor-info" class="d-none">
                            <h6 class="tumor-name"></h6>
                            <p class="tumor-description small"></p>
                            <div class="d-grid">
                                <a href="#" id="tumor-detail-link" class="btn btn-sm btn-outline-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">Surgical Approach</h5>
                    </div>
                    <div class="card-body">
                        <select id="approach-select" class="form-select mb-3">
                            <option value="">Select Approach</option>
                            {% for approach_type, approach in surgical_approaches.items() %}
                            <option value="{{ approach_type }}">{{ approach.name }}</option>
                            {% endfor %}
                        </select>
                        
                        <div id="approach-info" class="d-none">
                            <h6 class="approach-name"></h6>
                            <p class="approach-description small"></p>
                            <div class="d-grid">
                                <a href="#" id="approach-detail-link" class="btn btn-sm btn-outline-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">Visualization Options</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="show-brain" checked>
                            <label class="form-check-label" for="show-brain">Brain Tissue</label>
                        </div>
                        
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="show-tumor" checked>
                            <label class="form-check-label" for="show-tumor">Tumor</label>
                        </div>
                        
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="show-vessels">
                            <label class="form-check-label" for="show-vessels">Blood Vessels</label>
                        </div>
                        
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="show-nerves">
                            <label class="form-check-label" for="show-nerves">Nerves</label>
                        </div>
                        
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="show-approach">
                            <label class="form-check-label" for="show-approach">Approach Path</label>
                        </div>
                        
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="show-instrument">
                            <label class="form-check-label" for="show-instrument">Surgical Instrument</label>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">Simulation Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button id="play-animation" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>Simulate Approach
                            </button>
                            <button id="reset-animation" class="btn btn-secondary">
                                <i class="fas fa-undo me-2"></i>Reset
                            </button>
                            <button id="toggle-transparency" class="btn btn-info">
                                <i class="fas fa-low-vision me-2"></i>Toggle Transparency
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3 text-center">
                    <a href="/" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get tumor and approach data from the server
    const tumorTypes = {{ tumor_types|tojson }};
    const surgicalApproaches = {{ surgical_approaches|tojson }};
    
    // Setup the 3D scene
    const container = document.getElementById('simulator-canvas');
    let scene, camera, renderer, controls;
    let brainModel, tumorModel, vesselsModel, nervesModel, approachModel, instrumentModel;
    let animationInProgress = false;
    let brainTransparent = true;
    
    // Initialize the 3D scene
    function initScene() {
        // Create scene
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x111111);
        
        // Create camera
        camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
        camera.position.z = 200;
        
        // Create renderer
        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(container.clientWidth, container.clientHeight);
        container.appendChild(renderer.domElement);
        
        // Add controls
        controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.25;
        
        // Add lights
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(0, 1, 1);
        scene.add(directionalLight);
        
        // Create brain model
        const brainGeometry = new THREE.SphereGeometry(50, 32, 32);
        const brainMaterial = new THREE.MeshPhongMaterial({ 
            color: 0xe0a9a9,
            transparent: true,
            opacity: 0.7
        });
        brainModel = new THREE.Mesh(brainGeometry, brainMaterial);
        scene.add(brainModel);
        
        // Create empty models for other components
        tumorModel = new THREE.Group();
        scene.add(tumorModel);
        
        vesselsModel = createVesselsModel();
        vesselsModel.visible = false;
        scene.add(vesselsModel);
        
        nervesModel = createNervesModel();
        nervesModel.visible = false;
        scene.add(nervesModel);
        
        approachModel = new THREE.Group();
        approachModel.visible = false;
        scene.add(approachModel);
        
        instrumentModel = new THREE.Group();
        instrumentModel.visible = false;
        scene.add(instrumentModel);
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }
        animate();
        
        // Handle window resize
        window.addEventListener('resize', function() {
            camera.aspect = container.clientWidth / container.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(container.clientWidth, container.clientHeight);
        });
    }
    
    // Create blood vessels model
    function createVesselsModel() {
        const vesselsGroup = new THREE.Group();
        
        // Create main vessels
        const vesselGeometry = new THREE.CylinderGeometry(2, 2, 100, 8);
        const vesselMaterial = new THREE.MeshPhongMaterial({ color: 0xcc0000 });
        
        const vessel1 = new THREE.Mesh(vesselGeometry, vesselMaterial);
        vessel1.position.set(20, 0, 0);
        vessel1.rotation.z = Math.PI / 2;
        vesselsGroup.add(vessel1);
        
        const vessel2 = new THREE.Mesh(vesselGeometry, vesselMaterial);
        vessel2.position.set(-20, 0, 0);
        vessel2.rotation.z = Math.PI / 2;
        vesselsGroup.add(vessel2);
        
        const vessel3 = new THREE.Mesh(vesselGeometry, vesselMaterial);
        vessel3.position.set(0, 20, 0);
        vesselsGroup.add(vessel3);
        
        // Add smaller vessels
        const smallVesselGeometry = new THREE.CylinderGeometry(1, 1, 60, 8);
        
        for (let i = 0; i < 5; i++) {
            const angle = (i / 5) * Math.PI * 2;
            const x = Math.cos(angle) * 30;
            const y = Math.sin(angle) * 30;
            
            const smallVessel = new THREE.Mesh(smallVesselGeometry, vesselMaterial);
            smallVessel.position.set(x, y, 0);
            smallVessel.lookAt(0, 0, 0);
            smallVessel.rotateX(Math.PI / 2);
            vesselsGroup.add(smallVessel);
        }
        
        return vesselsGroup;
    }
    
    // Create nerves model
    function createNervesModel() {
        const nervesGroup = new THREE.Group();
        
        // Create main nerves
        const nerveGeometry = new THREE.CylinderGeometry(1, 1, 120, 8);
        const nerveMaterial = new THREE.MeshPhongMaterial({ color: 0xffff00 });
        
        const nerve1 = new THREE.Mesh(nerveGeometry, nerveMaterial);
        nerve1.position.set(10, -10, 0);
        nerve1.rotation.z = Math.PI / 4;
        nervesGroup.add(nerve1);
        
        const nerve2 = new THREE.Mesh(nerveGeometry, nerveMaterial);
        nerve2.position.set(-10, -10, 0);
        nerve2.rotation.z = -Math.PI / 4;
        nervesGroup.add(nerve2);
        
        // Add smaller nerves
        const smallNerveGeometry = new THREE.CylinderGeometry(0.5, 0.5, 80, 8);
        
        for (let i = 0; i < 3; i++) {
            const angle = (i / 3) * Math.PI;
            const x = Math.cos(angle) * 20;
            const y = Math.sin(angle) * 20 - 20;
            
            const smallNerve = new THREE.Mesh(smallNerveGeometry, nerveMaterial);
            smallNerve.position.set(x, y, 0);
            smallNerve.rotation.z = angle;
            nervesGroup.add(smallNerve);
        }
        
        return nervesGroup;
    }
    
    // Create tumor model based on selected tumor type
    function createTumorModel(tumorType) {
        // Remove existing tumor
        while (tumorModel.children.length > 0) {
            tumorModel.remove(tumorModel.children[0]);
        }
        
        if (!tumorType) return;
        
        // Create new tumor
        let tumorGeometry, tumorPosition, tumorColor;
        
        switch(tumorType) {
            case 'glioblastoma':
                tumorGeometry = new THREE.SphereGeometry(15, 32, 32);
                tumorPosition = new THREE.Vector3(30, 20, 30);
                tumorColor = 0xff0000;
                break;
            case 'meningioma':
                tumorGeometry = new THREE.SphereGeometry(12, 32, 32);
                tumorPosition = new THREE.Vector3(-20, 40, 10);
                tumorColor = 0x00ff00;
                break;
            case 'acoustic_neuroma':
                tumorGeometry = new THREE.SphereGeometry(8, 32, 32);
                tumorPosition = new THREE.Vector3(-40, -10, 20);
                tumorColor = 0x0000ff;
                break;
            case 'pituitary_adenoma':
                tumorGeometry = new THREE.SphereGeometry(10, 32, 32);
                tumorPosition = new THREE.Vector3(0, -40, 0);
                tumorColor = 0xffff00;
                break;
            default:
                return;
        }
        
        const tumorMaterial = new THREE.MeshPhongMaterial({ color: tumorColor });
        const tumor = new THREE.Mesh(tumorGeometry, tumorMaterial);
        tumor.position.copy(tumorPosition);
        tumorModel.add(tumor);
        tumorModel.visible = true;
        
        return tumorPosition;
    }
    
    // Create approach model based on selected approach type
    function createApproachModel(approachType, tumorPosition) {
        // Remove existing approach
        while (approachModel.children.length > 0) {
            approachModel.remove(approachModel.children[0]);
        }
        
        // Remove existing instrument
        while (instrumentModel.children.length > 0) {
            instrumentModel.remove(instrumentModel.children[0]);
        }
        
        if (!approachType || !tumorPosition) return;
        
        let approachStart, approachColor;
        
        switch(approachType) {
            case 'craniotomy':
                approachStart = new THREE.Vector3(
                    tumorPosition.x,
                    tumorPosition.y + 70,
                    tumorPosition.z
                );
                approachColor = 0x00ffff;
                break;
            case 'transsphenoidal':
                approachStart = new THREE.Vector3(
                    0,
                    -70,
                    20
                );
                approachColor = 0xff00ff;
                break;
            case 'endoscopic':
                approachStart = new THREE.Vector3(
                    tumorPosition.x,
                    tumorPosition.y + 70,
                    tumorPosition.z
                );
                approachColor = 0xffff00;
                break;
            case 'stereotactic':
                approachColor = 0x00ff00;
                // Create multiple radiation beams
                for (let i = 0; i < 8; i++) {
                    const angle = (i / 8) * Math.PI * 2;
                    const startX = Math.cos(angle) * 80;
                    const startY = Math.sin(angle) * 80;
                    
                    const beamGeometry = new THREE.CylinderGeometry(1, 1, 100, 8);
                    const beamMaterial = new THREE.MeshPhongMaterial({ 
                        color: approachColor,
                        transparent: true,
                        opacity: 0.5
                    });
                    const beam = new THREE.Mesh(beamGeometry, beamMaterial);
                    beam.position.set(startX / 2, startY / 2, 0);
                    beam.lookAt(tumorPosition);
                    beam.rotateX(Math.PI / 2);
                    approachModel.add(beam);
                }
                approachModel.visible = true;
                return;
            default:
                return;
        }
        
        // Create approach path
        const approachGeometry = new THREE.CylinderGeometry(3, 3, 
            approachStart.distanceTo(tumorPosition), 8);
        const approachMaterial = new THREE.MeshPhongMaterial({ 
            color: approachColor,
            transparent: true,
            opacity: 0.5
        });
        const approachPath = new THREE.Mesh(approachGeometry, approachMaterial);
        
        // Position and rotate the cylinder to connect start and end points
        approachPath.position.set(
            (approachStart.x + tumorPosition.x) / 2,
            (approachStart.y + tumorPosition.y) / 2,
            (approachStart.z + tumorPosition.z) / 2
        );
        
        // Calculate direction vector
        const direction = new THREE.Vector3().subVectors(tumorPosition, approachStart);
        const normal = new THREE.Vector3(0, 1, 0);
        
        // Align cylinder with direction
        approachPath.quaternion.setFromUnitVectors(normal, direction.normalize());
        
        approachModel.add(approachPath);
        approachModel.visible = true;
        
        // Create surgical instrument
        if (approachType !== 'stereotactic') {
            // Create a simple surgical instrument
            const instrumentGroup = new THREE.Group();
            
            // Instrument handle
            const handleGeometry = new THREE.CylinderGeometry(2, 2, 20, 16);
            const handleMaterial = new THREE.MeshPhongMaterial({ color: 0x888888 });
            const handle = new THREE.Mesh(handleGeometry, handleMaterial);
            handle.position.y = 10;
            instrumentGroup.add(handle);
            
            // Instrument tip
            const tipGeometry = new THREE.ConeGeometry(2, 10, 16);
            const tipMaterial = new THREE.MeshPhongMaterial({ color: 0xcccccc });
            const tip = new THREE.Mesh(tipGeometry, tipMaterial);
            tip.position.y = -5;
            instrumentGroup.add(tip);
            
            // Position the instrument at the start of the approach
            instrumentGroup.position.copy(approachStart);
            
            // Rotate to point toward the tumor
            instrumentGroup.lookAt(tumorPosition);
            instrumentGroup.rotateX(Math.PI);
            
            instrumentModel.add(instrumentGroup);
            instrumentModel.visible = document.getElementById('show-instrument').checked;
        }
        
        return approachStart;
    }
    
    // Initialize the scene
    initScene();
    
    // Handle tumor selection
    document.getElementById('tumor-select').addEventListener('change', function(e) {
        const tumorType = e.target.value;
        const tumorInfo = document.getElementById('tumor-info');
        
        if (tumorType) {
            const tumor = tumorTypes[tumorType];
            
            // Update tumor info
            tumorInfo.querySelector('.tumor-name').textContent = tumor.name;
            tumorInfo.querySelector('.tumor-description').textContent = tumor.description;
            tumorInfo.querySelector('#tumor-detail-link').href = `/tumor/${tumorType}`;
            tumorInfo.classList.remove('d-none');
            
            // Create tumor model
            const tumorPosition = createTumorModel(tumorType);
            
            // Update approach if already selected
            const approachType = document.getElementById('approach-select').value;
            if (approachType) {
                createApproachModel(approachType, tumorPosition);
            }
        } else {
            tumorInfo.classList.add('d-none');
            tumorModel.visible = false;
        }
    });
    
    // Handle approach selection
    document.getElementById('approach-select').addEventListener('change', function(e) {
        const approachType = e.target.value;
        const approachInfo = document.getElementById('approach-info');
        
        if (approachType) {
            const approach = surgicalApproaches[approachType];
            
            // Update approach info
            approachInfo.querySelector('.approach-name').textContent = approach.name;
            approachInfo.querySelector('.approach-description').textContent = approach.description;
            approachInfo.querySelector('#approach-detail-link').href = `/approach/${approachType}`;
            approachInfo.classList.remove('d-none');
            
            // Get tumor position
            let tumorPosition;
            if (tumorModel.children.length > 0) {
                tumorPosition = tumorModel.children[0].position.clone();
            } else {
                // Default position if no tumor selected
                tumorPosition = new THREE.Vector3(0, 0, 0);
            }
            
            // Create approach model
            createApproachModel(approachType, tumorPosition);
        } else {
            approachInfo.classList.add('d-none');
            approachModel.visible = false;
            instrumentModel.visible = false;
        }
    });
    
    // Handle visualization toggles
    document.getElementById('show-brain').addEventListener('change', function(e) {
        brainModel.visible = e.target.checked;
    });
    
    document.getElementById('show-tumor').addEventListener('change', function(e) {
        tumorModel.visible = e.target.checked;
    });
    
    document.getElementById('show-vessels').addEventListener('change', function(e) {
        vesselsModel.visible = e.target.checked;
    });
    
    document.getElementById('show-nerves').addEventListener('change', function(e) {
        nervesModel.visible = e.target.checked;
    });
    
    document.getElementById('show-approach').addEventListener('change', function(e) {
        approachModel.visible = e.target.checked;
    });
    
    document.getElementById('show-instrument').addEventListener('change', function(e) {
        instrumentModel.visible = e.target.checked;
    });
    
    // Handle simulation controls
    document.getElementById('play-animation').addEventListener('click', function() {
        if (animationInProgress || instrumentModel.children.length === 0 || 
            tumorModel.children.length === 0) return;
        
        animationInProgress = true;
        
        const instrument = instrumentModel.children[0];
        const startPosition = instrument.position.clone();
        const endPosition = tumorModel.children[0].position.clone();
        let progress = 0;
        
        function animateInstrument() {
            if (progress >= 1) {
                animationInProgress = false;
                return;
            }
            
            progress += 0.01;
            
            // Interpolate position
            instrument.position.lerpVectors(startPosition, endPosition, progress);
            
            requestAnimationFrame(animateInstrument);
        }
        
        animateInstrument();
    });
    
    document.getElementById('reset-animation').addEventListener('click', function() {
        if (instrumentModel.children.length === 0) return;
        
        animationInProgress = false;
        
        // Reset instrument position
        const approachType = document.getElementById('approach-select').value;
        const tumorType = document.getElementById('tumor-select').value;
        
        if (approachType && tumorType) {
            let tumorPosition;
            if (tumorModel.children.length > 0) {
                tumorPosition = tumorModel.children[0].position.clone();
            } else {
                return;
            }
            
            createApproachModel(approachType, tumorPosition);
        }
    });
    
    document.getElementById('toggle-transparency').addEventListener('click', function() {
        brainTransparent = !brainTransparent;
        
        if (brainTransparent) {
            brainModel.material.opacity = 0.7;
        } else {
            brainModel.material.opacity = 0.2;
        }
    });
    
    // Check for URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const tumorParam = urlParams.get('tumor');
    const approachParam = urlParams.get('approach');
    
    // Set initial selections if provided in URL
    if (tumorParam && tumorTypes[tumorParam]) {
        const tumorSelect = document.getElementById('tumor-select');
        tumorSelect.value = tumorParam;
        tumorSelect.dispatchEvent(new Event('change'));
    }
    
    if (approachParam && surgicalApproaches[approachParam]) {
        const approachSelect = document.getElementById('approach-select');
        approachSelect.value = approachParam;
        approachSelect.dispatchEvent(new Event('change'));
    }
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.simulator-container {
    height: calc(100vh - 56px);
    margin-top: -1.5rem;
    overflow: hidden;
}

#simulator-canvas {
    width: 100%;
    height: 100%;
    background-color: #111;
}

.simulator-controls {
    height: 100%;
    padding: 20px;
    background-color: #f8f9fa;
    overflow-y: auto;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-top-left-radius: 10px !important;
    border-top-right-radius: 10px !important;
}
</style>
{% endblock %}
