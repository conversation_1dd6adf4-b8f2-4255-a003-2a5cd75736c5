{% extends "layout.html" %}

{% block title %}Insights{% endblock %}

{% block content %}
<h1 class="mb-4"><i class="fas fa-chart-bar me-2"></i>Medical Insights</h1>

<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">Feature Importance in Heart Disease Prediction</h3>
            </div>
            <div class="card-body">
                <p>This chart shows the relative importance of different health metrics in predicting heart disease risk according to our model.</p>
                <canvas id="featureImportanceChart" width="400" height="250"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">Key Findings</h3>
            </div>
            <div class="card-body">
                <ul class="list-group">
                    <li class="list-group-item">
                        <h5><i class="fas fa-heart text-danger me-2"></i>Blood Pressure</h5>
                        <p>High blood pressure (hypertension) is a major risk factor for heart disease. It can damage arteries and make them less elastic.</p>
                    </li>
                    <li class="list-group-item">
                        <h5><i class="fas fa-calendar-alt text-warning me-2"></i>Age</h5>
                        <p>Age is a significant non-modifiable risk factor. As we age, our heart and blood vessels undergo changes that increase risk.</p>
                    </li>
                    <li class="list-group-item">
                        <h5><i class="fas fa-tint text-primary me-2"></i>Cholesterol</h5>
                        <p>High cholesterol can lead to plaque buildup in arteries, restricting blood flow to the heart.</p>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">Interactive Data Explorer</h3>
            </div>
            <div class="card-body">
                <p>Explore the relationships between different health metrics and heart disease risk in our dataset.</p>
                <div class="mb-3">
                    <label for="xAxis" class="form-label">X-Axis:</label>
                    <select class="form-select" id="xAxis">
                        <option value="age">Age</option>
                        <option value="blood_pressure">Blood Pressure</option>
                        <option value="cholesterol">Cholesterol</option>
                        <option value="blood_sugar">Blood Sugar</option>
                        <option value="bmi">BMI</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="yAxis" class="form-label">Y-Axis:</label>
                    <select class="form-select" id="yAxis">
                        <option value="blood_pressure">Blood Pressure</option>
                        <option value="age">Age</option>
                        <option value="cholesterol">Cholesterol</option>
                        <option value="blood_sugar">Blood Sugar</option>
                        <option value="bmi">BMI</option>
                    </select>
                </div>
                <canvas id="scatterPlot" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Feature Importance Chart
    const featureImportanceCtx = document.getElementById('featureImportanceChart').getContext('2d');

    const featureImportance = {
        'Age': {{ feature_importance.age }},
        'Blood Pressure': {{ feature_importance.blood_pressure }},
        'Cholesterol': {{ feature_importance.cholesterol }},
        'Blood Sugar': {{ feature_importance.blood_sugar }},
        'BMI': {{ feature_importance.bmi }}
    };

    const labels = Object.keys(featureImportance);
    const values = Object.values(featureImportance);

    new Chart(featureImportanceCtx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Feature Importance',
                data: values,
                backgroundColor: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)'
                ],
                borderColor: [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            plugins: {
                legend: {
                    display: false
                },
                title: {
                    display: true,
                    text: 'Relative Importance of Health Metrics in Heart Disease Risk'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `Importance: ${(context.raw * 100).toFixed(1)}%`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    max: 0.35,
                    title: {
                        display: true,
                        text: 'Importance Score'
                    },
                    ticks: {
                        callback: function(value) {
                            return (value * 100).toFixed(0) + '%';
                        }
                    }
                }
            }
        }
    });

    // Scatter Plot
    let scatterChart;
    const scatterCtx = document.getElementById('scatterPlot').getContext('2d');

    // Function to fetch data and update chart
    function updateChart() {
        const xAxis = document.getElementById('xAxis').value;
        const yAxis = document.getElementById('yAxis').value;

        fetch('/api/data')
            .then(response => response.json())
            .then(data => {
                // Prepare data for chart
                const chartData = {
                    datasets: [
                        {
                            label: 'Low Risk',
                            data: data.filter(item => item.heart_disease_risk === 0).map(item => ({
                                x: item[xAxis],
                                y: item[yAxis]
                            })),
                            backgroundColor: 'rgba(75, 192, 192, 0.6)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1,
                            pointRadius: 4
                        },
                        {
                            label: 'High Risk',
                            data: data.filter(item => item.heart_disease_risk === 1).map(item => ({
                                x: item[xAxis],
                                y: item[yAxis]
                            })),
                            backgroundColor: 'rgba(255, 99, 132, 0.6)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1,
                            pointRadius: 4
                        }
                    ]
                };

                // Destroy previous chart if it exists
                if (scatterChart) {
                    scatterChart.destroy();
                }

                // Create new chart
                scatterChart = new Chart(scatterCtx, {
                    type: 'scatter',
                    data: chartData,
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: `Relationship between ${xAxis.replace('_', ' ')} and ${yAxis.replace('_', ' ')}`
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `${context.dataset.label}: (${context.parsed.x}, ${context.parsed.y})`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: xAxis.replace('_', ' ')
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: yAxis.replace('_', ' ')
                                }
                            }
                        }
                    }
                });
            });
    }

    // Initial chart
    updateChart();

    // Update chart when selections change
    document.getElementById('xAxis').addEventListener('change', updateChart);
    document.getElementById('yAxis').addEventListener('change', updateChart);
});
</script>
{% endblock %}
